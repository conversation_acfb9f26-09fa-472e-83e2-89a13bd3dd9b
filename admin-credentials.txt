# Admin User Credentials for Positive7 Tourism
# Generated on: 2025-05-31T16:08:01.413Z
#
# IMPORTANT: Keep this file secure and do not commit to version control!

## SUPER_ADMIN USER
Email: <EMAIL>
Password: pratham1
Roles: super_admin
Status: existing

## CONTENT_MANAGER USER (Create manually via admin panel)
Email: <EMAIL>
Password: Content123!
Roles: content_manager
Status: to_be_created

## CUSTOMER_SUPPORT USER (Create manually via admin panel)
Email: <EMAIL>
Password: Support123!
Roles: customer_support
Status: to_be_created

## PHOTO_MANAGER USER (Create manually via admin panel)
Email: <EMAIL>
Password: Photos123!
Roles: photo_manager
Status: to_be_created

## Login Instructions:
1. Go to: http://localhost:3000/admin/login
2. Use the super admin credentials to login first
3. Navigate to Admin Users section to create additional users
4. Each user will see different admin sections based on their role

## Role Permissions:
- super_admin: Full access to all features including user management
- content_manager: Can manage trips, blogs, and view inquiries
- customer_support: Can handle inquiries and view content
- photo_manager: Can manage trip photos and view trips

## Creating Additional Users:
1. Login as super admin (<EMAIL>)
2. Go to Admin > Admin Users
3. Click "Add User" button
4. Use the email/password combinations above
5. Assign the appropriate roles

## Security Notes:
- Change these passwords in production
- Use strong passwords for production environments
- Regularly audit user access and permissions
