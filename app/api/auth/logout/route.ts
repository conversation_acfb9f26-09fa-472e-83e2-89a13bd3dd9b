import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseAuth } from '@/lib/auth-server';

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseAuth();

    // Sign out the user
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Logout error:', error);
      return NextResponse.json(
        { error: 'Failed to logout' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Logout failed', details: error.message },
      { status: 500 }
    );
  }
}
