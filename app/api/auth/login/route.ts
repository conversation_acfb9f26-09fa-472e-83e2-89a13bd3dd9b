import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseAuth, getAdminUserWithRoles } from '@/lib/auth-server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseAuth();

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      console.error('Authentication error:', authError);
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    // Check if user is an admin with roles
    const adminUser = await getAdminUserWithRoles(authData.user.id);

    if (!adminUser || !adminUser.is_active || adminUser.roles.length === 0) {
      // Sign out the user since they're not an admin
      await supabase.auth.signOut();
      return NextResponse.json(
        { error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }

    // Update last login time
    await supabase
      .from('admin_profiles')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', authData.user.id);

    return NextResponse.json({
      success: true,
      user: {
        id: adminUser.id,
        email: authData.user.email,
        username: adminUser.username,
        full_name: adminUser.full_name,
        roles: adminUser.roles.map(role => ({
          name: role.name,
          permissions: role.permissions
        }))
      }
    });

  } catch (error: any) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed', details: error.message },
      { status: 500 }
    );
  }
}
