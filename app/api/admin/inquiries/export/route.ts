import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries/export - Export inquiries as CSV with date filters
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse date filter parameter
    const dateFilter = searchParams.get('dateFilter') || 'all'; // 'today', 'month', 'all'
    
    // Build query
    let query = supabase
      .from('inquiries')
      .select('*, trips(title)')
      .order('created_at', { ascending: false });

    // Apply date filters
    const now = new Date();
    
    if (dateFilter === 'today') {
      // Filter inquiries from today
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();
      query = query.gte('created_at', todayStart).lt('created_at', todayEnd);
    } else if (dateFilter === 'month') {
      // Filter inquiries from the current month
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      const nextMonthStart = new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString();
      query = query.gte('created_at', monthStart).lt('created_at', nextMonthStart);
    }
    // 'all' doesn't need any additional filtering

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries for export:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    // Check if we have any inquiries to export
    if (!inquiries || inquiries.length === 0) {
      return NextResponse.json(
        { error: `No inquiries found for the selected period (${dateFilter})` },
        { status: 404 }
      );
    }

    // Create CSV content
    const csvHeader = [
      'ID', 
      'Name', 
      'Email', 
      'Phone', 
      'Subject', 
      'Message', 
      'Inquiry Type',
      'Trip', 
      'Status', 
      'Admin Notes', 
      'Created At', 
      'Updated At'
    ].join(',');

    const csvRows = inquiries.map(inquiry => {
      // Format the data and handle potential commas in text fields
      return [
        inquiry.id,
        formatCSVField(inquiry.name),
        formatCSVField(inquiry.email),
        formatCSVField(inquiry.phone || ''),
        formatCSVField(inquiry.subject || ''),
        formatCSVField(inquiry.message),
        formatCSVField(inquiry.inquiry_type || ''),
        formatCSVField(inquiry.trips?.title || 'General Inquiry'),
        inquiry.status,
        formatCSVField(inquiry.admin_notes || ''),
        new Date(inquiry.created_at).toISOString(),
        new Date(inquiry.updated_at).toISOString()
      ].join(',');
    });

    const csvContent = [csvHeader, ...csvRows].join('\n');

    // Generate filename based on the date filter
    let filename;
    switch (dateFilter) {
      case 'today':
        filename = `inquiries-${now.toISOString().split('T')[0]}.csv`;
        break;
      case 'month':
        filename = `inquiries-${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}.csv`;
        break;
      default:
        filename = `all-inquiries-${now.toISOString().split('T')[0]}.csv`;
    }

    // Return CSV response
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/inquiries/export:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to format field values for CSV
// Escapes quotes and wraps values containing commas in quotes
function formatCSVField(value: string): string {
  if (!value) return '';
  
  // Replace quotes with double quotes (CSV standard for escaping quotes)
  const escaped = value.replace(/"/g, '""');
  
  // Wrap in quotes if the value contains commas, quotes, or newlines
  if (escaped.includes(',') || escaped.includes('"') || escaped.includes('\n')) {
    return `"${escaped}"`;
  }
  
  return escaped;
} 