export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      admin_sessions: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          token: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          token: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          token?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_users: {
        Row: {
          created_at: string | null
          email: string
          id: string
          password_hash: string
          username: string
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          password_hash: string
          username: string
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          password_hash?: string
          username?: string
        }
        Relationships: []
      }
      blog_posts: {
        Row: {
          author_id: string | null
          category: string | null
          content: string
          created_at: string | null
          excerpt: string | null
          featured_image_url: string | null
          id: string
          is_published: boolean | null
          published_at: string | null
          seo_description: string | null
          seo_title: string | null
          slug: string
          tags: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          content: string
          created_at?: string | null
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_title?: string | null
          slug: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          content?: string
          created_at?: string | null
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_title?: string | null
          slug?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      inquiries: {
        Row: {
          admin_notes: string | null
          created_at: string | null
          email: string
          id: string
          inquiry_type: string | null
          message: string
          name: string
          phone: string | null
          responded_at: string | null
          status: Database["public"]["Enums"]["inquiry_status"] | null
          subject: string | null
          trip_id: string | null
          updated_at: string | null
        }
        Insert: {
          admin_notes?: string | null
          created_at?: string | null
          email: string
          id?: string
          inquiry_type?: string | null
          message: string
          name: string
          phone?: string | null
          responded_at?: string | null
          status?: Database["public"]["Enums"]["inquiry_status"] | null
          subject?: string | null
          trip_id?: string | null
          updated_at?: string | null
        }
        Update: {
          admin_notes?: string | null
          created_at?: string | null
          email?: string
          id?: string
          inquiry_type?: string | null
          message?: string
          name?: string
          phone?: string | null
          responded_at?: string | null
          status?: Database["public"]["Enums"]["inquiry_status"] | null
          subject?: string | null
          trip_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inquiries_trip_id_fkey"
            columns: ["trip_id"]
            isOneToOne: false
            referencedRelation: "trips"
            referencedColumns: ["id"]
          },
        ]
      }
      newsletter_subscriptions: {
        Row: {
          email: string
          id: string
          is_active: boolean | null
          name: string | null
          subscribed_at: string | null
          unsubscribed_at: string | null
        }
        Insert: {
          email: string
          id?: string
          is_active?: boolean | null
          name?: string | null
          subscribed_at?: string | null
          unsubscribed_at?: string | null
        }
        Update: {
          email?: string
          id?: string
          is_active?: boolean | null
          name?: string | null
          subscribed_at?: string | null
          unsubscribed_at?: string | null
        }
        Relationships: []
      }
      team_members: {
        Row: {
          bio: string
          created_at: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          name: string
          position: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          bio: string
          created_at?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name: string
          position: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          bio?: string
          created_at?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name?: string
          position?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      testimonials: {
        Row: {
          content: string
          created_at: string | null
          email: string | null
          id: string
          image_url: string | null
          is_approved: boolean | null
          is_featured: boolean | null
          name: string
          rating: number | null
          title: string | null
          trip_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          email?: string | null
          id?: string
          image_url?: string | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          name: string
          rating?: number | null
          title?: string | null
          trip_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          email?: string | null
          id?: string
          image_url?: string | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          name?: string
          rating?: number | null
          title?: string | null
          trip_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "testimonials_trip_id_fkey"
            columns: ["trip_id"]
            isOneToOne: false
            referencedRelation: "trips"
            referencedColumns: ["id"]
          },
        ]
      }
      trip_photos_details: {
        Row: {
          access_password: string | null
          created_at: string | null
          drive_folder_id: string | null
          featured_image_url: string | null
          google_drive_link: string | null
          id: string
          trip_description: string | null
          trip_name: string
          updated_at: string | null
        }
        Insert: {
          access_password?: string | null
          created_at?: string | null
          drive_folder_id?: string | null
          featured_image_url?: string | null
          google_drive_link?: string | null
          id?: string
          trip_description?: string | null
          trip_name: string
          updated_at?: string | null
        }
        Update: {
          access_password?: string | null
          created_at?: string | null
          drive_folder_id?: string | null
          featured_image_url?: string | null
          google_drive_link?: string | null
          id?: string
          trip_description?: string | null
          trip_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      trips: {
        Row: {
          activities: string[] | null
          available_dates: string[] | null
          available_from: string | null
          available_to: string | null
          benefits: string[] | null
          cancellation_policy: Json | null
          category: string | null
          commercial_price: number | null
          created_at: string | null
          days: number
          description: string | null
          destination: string
          detailed_description: string | null
          difficulty: Database["public"]["Enums"]["trip_difficulty"]
          drop_location: string | null
          exclusions: string[] | null
          featured_image_url: string | null
          id: string
          inclusions: string[] | null
          is_active: boolean | null
          is_featured: boolean | null
          is_trek: boolean | null
          itinerary: Json | null
          max_age: number | null
          min_age: number | null
          mode_of_travel: string | null
          nights: number
          optional_activities: string[] | null
          payment_terms: string | null
          pickup_location: string | null
          price_per_person: number
          property_used: string | null
          safety_supervision: string[] | null
          slug: string
          special_notes: string[] | null
          things_to_carry: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          activities?: string[] | null
          available_dates?: string[] | null
          available_from?: string | null
          available_to?: string | null
          benefits?: string[] | null
          cancellation_policy?: Json | null
          category?: string | null
          commercial_price?: number | null
          created_at?: string | null
          days?: number
          description?: string | null
          destination: string
          detailed_description?: string | null
          difficulty: Database["public"]["Enums"]["trip_difficulty"]
          drop_location?: string | null
          exclusions?: string[] | null
          featured_image_url?: string | null
          id?: string
          inclusions?: string[] | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_trek?: boolean | null
          itinerary?: Json | null
          max_age?: number | null
          min_age?: number | null
          mode_of_travel?: string | null
          nights?: number
          optional_activities?: string[] | null
          payment_terms?: string | null
          pickup_location?: string | null
          price_per_person: number
          property_used?: string | null
          safety_supervision?: string[] | null
          slug: string
          special_notes?: string[] | null
          things_to_carry?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          activities?: string[] | null
          available_dates?: string[] | null
          available_from?: string | null
          available_to?: string | null
          benefits?: string[] | null
          cancellation_policy?: Json | null
          category?: string | null
          commercial_price?: number | null
          created_at?: string | null
          days?: number
          description?: string | null
          destination?: string
          detailed_description?: string | null
          difficulty?: Database["public"]["Enums"]["trip_difficulty"]
          drop_location?: string | null
          exclusions?: string[] | null
          featured_image_url?: string | null
          id?: string
          inclusions?: string[] | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_trek?: boolean | null
          itinerary?: Json | null
          max_age?: number | null
          min_age?: number | null
          mode_of_travel?: string | null
          nights?: number
          optional_activities?: string[] | null
          payment_terms?: string | null
          pickup_location?: string | null
          price_per_person?: number
          property_used?: string | null
          safety_supervision?: string[] | null
          slug?: string
          special_notes?: string[] | null
          things_to_carry?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      citext: {
        Args: { "": boolean } | { "": string } | { "": unknown }
        Returns: string
      }
      citext_hash: {
        Args: { "": string }
        Returns: number
      }
      citextin: {
        Args: { "": unknown }
        Returns: string
      }
      citextout: {
        Args: { "": string }
        Returns: unknown
      }
      citextrecv: {
        Args: { "": unknown }
        Returns: string
      }
      citextsend: {
        Args: { "": string }
        Returns: string
      }
      delete_admin_session: {
        Args: { token_param: string }
        Returns: undefined
      }
      insert_trip_photo: {
        Args: { p_image_url: string; p_is_watermarked?: boolean }
        Returns: string
      }
    }
    Enums: {
      booking_status: "pending" | "confirmed" | "cancelled" | "completed"
      inquiry_status: "new" | "in_progress" | "resolved" | "closed"
      trip_difficulty: "easy" | "moderate" | "challenging" | "extreme"
      user_role: "customer" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
