/**
 * Migration script to create the first admin user in Supabase Auth
 * Run this script after setting up the new authentication system
 * 
 * Usage: node scripts/migrate-admin-user.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const adminEmail = process.env.ADMIN_EMAIL;
const adminPassword = process.env.ADMIN_PASSWORD;

if (!supabaseUrl || !supabaseServiceKey || !adminEmail || !adminPassword) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function migrateAdminUser() {
  try {
    console.log('Starting admin user migration...');

    // Check if user already exists
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      throw new Error(`Failed to list users: ${listError.message}`);
    }

    const existingUser = existingUsers.users.find(user => user.email === adminEmail);
    
    if (existingUser) {
      console.log(`User ${adminEmail} already exists with ID: ${existingUser.id}`);
      
      // Check if they have admin roles
      const { data: userRoles, error: rolesError } = await supabase
        .from('admin_user_roles')
        .select('admin_roles(name)')
        .eq('user_id', existingUser.id);

      if (rolesError) {
        console.error('Error checking user roles:', rolesError);
      } else if (userRoles.length === 0) {
        console.log('User exists but has no admin roles. Assigning super_admin role...');
        await assignSuperAdminRole(existingUser.id);
      } else {
        console.log('User already has admin roles:', userRoles.map(ur => ur.admin_roles.name));
      }
      
      return;
    }

    // Create new admin user
    console.log(`Creating admin user: ${adminEmail}`);
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: adminEmail,
      password: adminPassword,
      user_metadata: {
        full_name: 'Super Administrator',
        username: 'admin'
      },
      email_confirm: true
    });

    if (authError) {
      throw new Error(`Failed to create user: ${authError.message}`);
    }

    console.log(`User created successfully with ID: ${authData.user.id}`);

    // Assign super_admin role
    await assignSuperAdminRole(authData.user.id);

    console.log('Admin user migration completed successfully!');
    console.log(`You can now login with:`);
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);

  } catch (error) {
    console.error('Migration failed:', error.message);
    process.exit(1);
  }
}

async function assignSuperAdminRole(userId) {
  // Get super_admin role ID
  const { data: superAdminRole, error: roleError } = await supabase
    .from('admin_roles')
    .select('id')
    .eq('name', 'super_admin')
    .single();

  if (roleError) {
    throw new Error(`Failed to find super_admin role: ${roleError.message}`);
  }

  // Assign role to user
  const { error: assignError } = await supabase
    .from('admin_user_roles')
    .insert({
      user_id: userId,
      role_id: superAdminRole.id
    });

  if (assignError) {
    throw new Error(`Failed to assign super_admin role: ${assignError.message}`);
  }

  console.log('Super admin role assigned successfully');
}

// Run the migration
migrateAdminUser();
