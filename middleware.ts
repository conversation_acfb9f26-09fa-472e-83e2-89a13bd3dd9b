import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from './lib/supabase-server';

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

// Simple in-memory rate limiter for login attempts
// This would be better implemented with Redis in production
const loginAttempts = new Map<string, { count: number, timestamp: number }>();
const MAX_ATTEMPTS = 5;
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes

export async function middleware(request: NextRequest) {
  // Create response with security headers
  const response = NextResponse.next({
    headers: {
      // Cache control headers
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store',
      
      // Security headers
      'Content-Security-Policy': 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com https://*.googletagmanager.com https://*.google-analytics.com; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "img-src 'self' data: https: blob:; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "connect-src 'self' https://*.supabase.co https://soaoagcuubtzojytoati.supabase.co wss://soaoagcuubtzojytoati.supabase.co https://*.googleapis.com https://*.google-analytics.com; " +
        "frame-src 'self' https://www.google.com https://www.youtube.com;",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), interest-cohort=()',
      'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload'
    }
  });

  const url = request.nextUrl.clone();
  
  // Rate limit login attempts
  if (url.pathname === '/api/admin/login') {
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    
    // Clean up old entries
    Array.from(loginAttempts.entries()).forEach(([key, data]) => {
      if (now - data.timestamp > WINDOW_MS) {
        loginAttempts.delete(key);
      }
    });
    
    const attempts = loginAttempts.get(ip);
    
    if (attempts && attempts.count >= MAX_ATTEMPTS && now - attempts.timestamp < WINDOW_MS) {
      return new NextResponse(
        JSON.stringify({ error: 'Too many login attempts. Please try again later.' }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Update attempts counter
    loginAttempts.set(ip, { 
      count: (attempts?.count || 0) + 1, 
      timestamp: attempts?.timestamp || now 
    });
  }

  // Check if request is for admin routes
  if (url.pathname.startsWith('/admin') && url.pathname !== '/admin/login') {
    // Get session token from cookie
    const sessionToken = request.cookies.get('admin_session')?.value;

    if (!sessionToken) {
      // Redirect to login page if no session token
      console.log('No session token found, redirecting to login');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }

    try {
      // Verify session token
      const supabase = createServerSupabase();
      const { data: session, error } = await supabase
        .from('admin_sessions')
        .select('id, user_id, expires_at')
        .eq('token', sessionToken)
        .single();

      if (error || !session) {
        // Invalid session token, redirect to login
        console.error('Invalid session token:', error);
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }

      // Check if session is expired
      const now = new Date();
      const expiresAt = new Date(session.expires_at);
      
      if (now > expiresAt) {
        // Session expired, redirect to login
        console.log('Session expired, redirecting to login');
        
        // Clean up expired session
        await supabase
          .from('admin_sessions')
          .delete()
          .eq('token', sessionToken);
          
        // Clear the cookie
        const redirectResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        redirectResponse.cookies.delete('admin_session');
        
        return redirectResponse;
      }
      
      // Get user info to ensure the user still exists
      const { data: user, error: userError } = await supabase
        .from('admin_users')
        .select('id')
        .eq('id', session.user_id)
        .single();
        
      if (userError || !user) {
        // User no longer exists, redirect to login
        console.error('User not found:', userError);
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }
    } catch (error) {
      console.error('Error verifying admin session:', error);
      // On error, redirect to login
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  return response;
} 