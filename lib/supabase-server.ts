import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

// Create a server-side Supabase client with admin privileges
// Use this for querying tables directly from the server
export function createServerSupabase() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Singleton instance for client-side Supabase client
let supabaseClientInstance: ReturnType<typeof createClient<Database>> | null = null

// Create a client-side Supabase client
// Use this for user authentication and client-side operations
export function createClientSupabase() {
  if (supabaseClientInstance) {
    return supabaseClientInstance
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables')
  }

  supabaseClientInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      storageKey: 'supabase-auth-token',
      storage: {
        getItem: (key) => {
          if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem(key)
          }
          return null
        },
        setItem: (key, value) => {
          if (typeof window !== 'undefined') {
            window.sessionStorage.setItem(key, value)
          }
        },
        removeItem: (key) => {
          if (typeof window !== 'undefined') {
            window.sessionStorage.removeItem(key)
          }
        },
      },
    }
  })
  
  return supabaseClientInstance
}
